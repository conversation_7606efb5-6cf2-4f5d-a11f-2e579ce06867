<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>CIH99 - Video Downloader Tool</title><style>body {
      font-family: Arial, sans-serif;
      background-color: #EFEBE0;
      color: #333;
      text-align: center;
      /* padding: 15px; */
      width: 550px;
      overflow-x: hidden;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      height: auto;
    }
    h1 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    p {
      font-size: 14px;
      margin: 5px 0;
      color: #555;
    }
    .video-placeholder {
      display: inline-flex;
      align-items: center;

    }

    .video-placeholder img{
      width: 100%;
      margin: auto;
    }
    /* Removed unused button styles */
    .small-text {
      font-size: 14px;
      margin-top: 10px;
    }

    .bottom-text{
      margin-top: 30px;
    }

    /* Removed unused icon styles */

    #userId{
      font-size: 16px;
      font-weight: 700;
    }

    /* .buttonSpan{
      width: 140px;
    } */

    .sj-tip{
      font-size: 17px;
      color: red;
     }

     #GuestDiv,#MemberDiv,#ProMemberDiv{
      display: none !important;
     }

     /* نظام الحماية - CIH99 Protection System */
     .protection-container {
       background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
       border-radius: 15px;
       padding: 20px;
       margin: 20px 0;
       box-shadow: 0 8px 32px rgba(0,0,0,0.1);
       border: 1px solid rgba(255,255,255,0.2);
     }

     .protection-header {
       color: white;
       font-size: 18px;
       font-weight: bold;
       margin-bottom: 15px;
       display: flex;
       align-items: center;
       justify-content: center;
       gap: 10px;
     }

     .status-indicator {
       display: flex;
       align-items: center;
       justify-content: center;
       gap: 10px;
       margin: 15px 0;
       padding: 10px;
       border-radius: 10px;
       font-weight: bold;
     }

     .status-active {
       background-color: rgba(76, 175, 80, 0.2);
       color: #4CAF50;
       border: 2px solid #4CAF50;
     }

     .status-inactive {
       background-color: rgba(244, 67, 54, 0.2);
       color: #f44336;
       border: 2px solid #f44336;
     }

     .toggle-container {
       display: flex;
       align-items: center;
       justify-content: center;
       gap: 15px;
       margin: 20px 0;
     }

     .toggle-switch {
       position: relative;
       width: 60px;
       height: 30px;
       background-color: #ccc;
       border-radius: 15px;
       cursor: pointer;
       transition: background-color 0.3s;
     }

     .toggle-switch.active {
       background-color: #4CAF50;
     }

     .toggle-slider {
       position: absolute;
       top: 3px;
       left: 3px;
       width: 24px;
       height: 24px;
       background-color: white;
       border-radius: 50%;
       transition: transform 0.3s;
       box-shadow: 0 2px 4px rgba(0,0,0,0.2);
     }

     .toggle-switch.active .toggle-slider {
       transform: translateX(30px);
     }

     .toggle-label {
       color: white;
       font-weight: bold;
       font-size: 16px;
     }

     .password-section {
       display: block;
       margin-top: 20px;
       padding: 20px;
       background: rgba(255,255,255,0.1);
       border-radius: 10px;
       backdrop-filter: blur(10px);
     }

     .password-input {
       width: 100%;
       max-width: 300px;
       padding: 12px;
       border: 2px solid rgba(255,255,255,0.3);
       border-radius: 8px;
       background: rgba(255,255,255,0.9);
       font-size: 16px;
       text-align: center;
       margin-bottom: 15px;
       outline: none;
       transition: border-color 0.3s;
     }

     .password-input:focus {
       border-color: #4CAF50;
     }

     .submit-btn {
       background: linear-gradient(45deg, #4CAF50, #45a049);
       color: white;
       border: none;
       padding: 12px 30px;
       border-radius: 8px;
       font-size: 16px;
       font-weight: bold;
       cursor: pointer;
       transition: transform 0.2s, box-shadow 0.2s;
       box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
     }

     .submit-btn:hover {
       transform: translateY(-2px);
       box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
     }

     .submit-btn:active {
       transform: translateY(0);
     }

     .message-box {
       margin-top: 15px;
       padding: 12px;
       border-radius: 8px;
       font-weight: bold;
       display: none;
       animation: fadeIn 0.3s ease-in;
     }

     .message-box.success {
       background-color: rgba(76, 175, 80, 0.2);
       color: #4CAF50;
       border: 2px solid #4CAF50;
     }

     .message-box.error {
       background-color: rgba(244, 67, 54, 0.2);
       color: #f44336;
       border: 2px solid #f44336;
     }

     @keyframes fadeIn {
       from { opacity: 0; transform: translateY(-10px); }
       to { opacity: 1; transform: translateY(0); }
     }

     .protection-info {
       color: rgba(255,255,255,0.9);
       font-size: 14px;
       margin-top: 15px;
       line-height: 1.5;
     }</style></head><body><div class="container"><div class="video-placeholder"><img src="../assets/imgs/optionlogo.png"/></div><h1 style="color: #2E7D32; font-size: 24px; margin: 20px 0;">🚀 CIH99 Video Downloader</h1>

<!-- نظام الحماية - CIH99 Protection System -->
<div class="protection-container">
  <div class="protection-header">
    🔐 نظام الحماية المتقدم - CIH99 Protection System
  </div>

  <div id="statusText" class="status-indicator status-inactive">
    🔴 معطل - Extension Disabled
  </div>

  <div class="toggle-container">
    <span class="toggle-label">تفعيل الإضافة</span>
    <div id="toggleSwitch" class="toggle-switch">
      <div class="toggle-slider"></div>
    </div>
  </div>

  <div id="passwordSection" class="password-section">
    <h4 style="color: white; margin-bottom: 15px;">🔑 يرجى إدخال كلمة المرور للتفعيل</h4>
    <p style="color: rgba(255,255,255,0.8); font-size: 14px; margin-bottom: 10px;">
      Please enter the activation password
    </p>
    <div style="background: rgba(0,150,255,0.2); border: 1px solid #0096FF; border-radius: 5px; padding: 8px; margin-bottom: 15px;">
      <p style="color: #87CEEB; font-size: 12px; margin: 0; text-align: center;">
        💡 <strong>كلمة المرور الحالية:</strong> CIH99-95DF-99BB-O299-AB99<br>
        💡 <strong>Current Password:</strong> CIH99-95DF-99BB-O299-AB99
      </p>
    </div>

    <input type="password" id="passwordInput" class="password-input" placeholder="أدخل كلمة المرور - Enter Password">
    <br>
    <button id="submitPassword" class="submit-btn">تأكيد - Verify</button>
    <div id="messageBox" class="message-box">
      <span id="messageText"></span>
    </div>
  </div>

  <div class="protection-info">
    🛡️ نظام حماية متقدم يتطلب كلمة مرور للتفعيل<br>
    🔄 مراقبة مستمرة لتغييرات كلمة المرور<br>
    ⚡ حماية تلقائية ضد الاستخدام غير المصرح به
  </div>
</div>

<h2 style="color: #c733a5; font-size: 16px;">Professional Course Video Downloader - Please refresh the page if you can't see the download button.</h2><p class="small-text bottom-text">Your ID:<span id="userId"></span><br/><strong>© 2025 CIH99 - All Rights Reserved</strong></p><div style="margin: 1rem 0;">
<div style="text-align: center; padding: 20px; background-color: #e8f5e8; border-radius: 10px; border: 2px solid #4CAF50;">
    <h3 style="color: #2e7d32; margin: 0 0 10px 0;">🎉 CIH99 Professional Video Downloader</h3>
    <p style="color: #388e3c; font-size: 16px; margin: 0;">✨ Unlimited Downloads • 🎯 Highest Quality • ⚡ Lightning Fast<br/>
    <strong>Download unlimited course videos without any restrictions!</strong></p>
</div>
</div></div><script src="js/simple-activation-system.js"></script><script src="js/option.js"></script></body></html>