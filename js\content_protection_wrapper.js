/**
 * Content Script Protection Wrapper
 * يلف الوظائف الأصلية للإضافة بنظام الحماية
 */

// متغيرات الحماية
let isProtectionActive = false;
let originalFunctions = {};

/**
 * تهيئة نظام الحماية للمحتوى
 */
async function initContentProtection() {
    try {
        console.log('🔐 تهيئة حماية المحتوى...');
        
        // التحقق من حالة الحماية
        await checkContentProtectionStatus();
        
        // إعداد مستمعي الأحداث
        setupContentProtectionListeners();
        
        // مراقبة دورية
        setInterval(checkContentProtectionStatus, 10000); // كل 10 ثوانٍ
        
        console.log('✅ تم تهيئة حماية المحتوى');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة حماية المحتوى:', error);
    }
}

/**
 * التحقق من حالة الحماية
 */
async function checkContentProtectionStatus() {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'getStatus'
        });
        
        if (response && response.enabled && response.passwordVerified) {
            if (!isProtectionActive) {
                isProtectionActive = true;
                enableContentFeatures();
                console.log('✅ تم تفعيل ميزات المحتوى');
            }
        } else {
            if (isProtectionActive) {
                isProtectionActive = false;
                disableContentFeatures();
                console.log('🚫 تم تعطيل ميزات المحتوى');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص حالة الحماية:', error);
        isProtectionActive = false;
        disableContentFeatures();
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupContentProtectionListeners() {
    // مستمع رسائل من background
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'protectionStatusChanged') {
            isProtectionActive = message.enabled;
            if (message.enabled) {
                enableContentFeatures();
            } else {
                disableContentFeatures();
            }
            sendResponse({ success: true });
        }
        return true;
    });
}

/**
 * تفعيل ميزات المحتوى
 */
function enableContentFeatures() {
    // إضافة مؤشر الحالة
    addStatusIndicator(true);
    
    // السماح للكود الأصلي بالعمل
    enableOriginalFunctionality();
    
    // إضافة أزرار التحميل إذا لم تكن موجودة
    setTimeout(() => {
        addDownloadButtonsIfNeeded();
    }, 2000);
}

/**
 * تعطيل ميزات المحتوى
 */
function disableContentFeatures() {
    // إضافة مؤشر الحالة
    addStatusIndicator(false);
    
    // تعطيل الكود الأصلي
    disableOriginalFunctionality();
    
    // إزالة أزرار التحميل
    removeDownloadButtons();
}

/**
 * إضافة مؤشر الحالة
 */
function addStatusIndicator(active) {
    // إزالة المؤشر السابق
    const existingIndicator = document.querySelector('.cih99-protection-status');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    
    // إنشاء مؤشر جديد
    const indicator = document.createElement('div');
    indicator.className = 'cih99-protection-status';
    indicator.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        z-index: 10000;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    `;
    
    if (active) {
        indicator.style.background = 'rgba(76, 175, 80, 0.9)';
        indicator.style.color = 'white';
        indicator.innerHTML = '🟢 CIH99 Active';
    } else {
        indicator.style.background = 'rgba(244, 67, 54, 0.9)';
        indicator.style.color = 'white';
        indicator.innerHTML = '🔴 CIH99 Disabled';
    }
    
    document.body.appendChild(indicator);
    
    // إزالة المؤشر بعد 3 ثوانٍ
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 3000);
}

/**
 * تفعيل الوظائف الأصلية
 */
function enableOriginalFunctionality() {
    // السماح للكود الأصلي بالعمل
    // هذا يعتمد على كيفية عمل الكود الأصلي
    console.log('🔓 تفعيل الوظائف الأصلية');
}

/**
 * تعطيل الوظائف الأصلية
 */
function disableOriginalFunctionality() {
    // منع الكود الأصلي من العمل
    console.log('🔒 تعطيل الوظائف الأصلية');
}

/**
 * إضافة أزرار التحميل إذا لم تكن موجودة
 */
function addDownloadButtonsIfNeeded() {
    if (!isProtectionActive) return;
    
    // البحث عن عناصر الفيديو
    const videoElements = document.querySelectorAll('video, [data-purpose="video-viewer"], .video-player');
    
    videoElements.forEach((element, index) => {
        // التحقق من وجود زر التحميل مسبقاً
        const existingBtn = element.parentElement?.querySelector('.cih99-download-btn');
        if (existingBtn) return;
        
        // إنشاء زر التحميل
        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'cih99-download-btn';
        downloadBtn.innerHTML = '⬇️ Download';
        downloadBtn.title = 'Download Video - CIH99 Downloader';
        downloadBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            z-index: 9999;
            transition: all 0.3s ease;
            font-size: 12px;
        `;
        
        // إضافة تأثيرات hover
        downloadBtn.addEventListener('mouseenter', () => {
            downloadBtn.style.transform = 'scale(1.05)';
            downloadBtn.style.boxShadow = '0 4px 15px rgba(76, 175, 80, 0.4)';
        });
        
        downloadBtn.addEventListener('mouseleave', () => {
            downloadBtn.style.transform = 'scale(1)';
            downloadBtn.style.boxShadow = 'none';
        });
        
        // إضافة مستمع النقر
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleProtectedDownload(element);
        });
        
        // إضافة الزر للصفحة
        if (element.parentElement) {
            element.parentElement.style.position = 'relative';
            element.parentElement.appendChild(downloadBtn);
        }
    });
}

/**
 * إزالة أزرار التحميل
 */
function removeDownloadButtons() {
    const downloadBtns = document.querySelectorAll('.cih99-download-btn');
    downloadBtns.forEach(btn => btn.remove());
}

/**
 * معالج التحميل المحمي
 */
async function handleProtectedDownload(videoElement) {
    if (!isProtectionActive) {
        showNotification('Extension is disabled. Please activate it first.', 'error');
        return;
    }
    
    try {
        console.log('⬇️ بدء التحميل المحمي...');
        
        // الحصول على معلومات الفيديو
        const videoInfo = {
            url: videoElement.src || videoElement.currentSrc || window.location.href,
            title: document.title || 'Unknown Video',
            quality: 'auto'
        };
        
        // إرسال طلب التحميل
        const response = await chrome.runtime.sendMessage({
            action: 'download',
            ...videoInfo
        });
        
        if (response && response.success) {
            showNotification('Download started successfully!', 'success');
        } else {
            showNotification(response?.error || 'Download failed', 'error');
        }
        
    } catch (error) {
        console.error('❌ خطأ في التحميل:', error);
        showNotification('Download error occurred', 'error');
    }
}

/**
 * عرض إشعار
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 70px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: bold;
        z-index: 10001;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    if (type === 'success') {
        notification.style.background = '#4CAF50';
        notification.style.color = 'white';
    } else if (type === 'error') {
        notification.style.background = '#f44336';
        notification.style.color = 'white';
    } else {
        notification.style.background = '#2196F3';
        notification.style.color = 'white';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// تهيئة النظام عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initContentProtection);
} else {
    initContentProtection();
}

// مراقبة تغييرات DOM لإضافة أزرار جديدة
const observer = new MutationObserver((mutations) => {
    if (isProtectionActive) {
        let shouldAddButtons = false;
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const videos = node.querySelectorAll ? node.querySelectorAll('video, [data-purpose="video-viewer"]') : [];
                    if (videos.length > 0) {
                        shouldAddButtons = true;
                    }
                }
            });
        });
        
        if (shouldAddButtons) {
            setTimeout(addDownloadButtonsIfNeeded, 1000);
        }
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('🔐 تم تحميل Content Protection Wrapper - CIH99');
