/**
 * Content Script محمي بنظام CIH99 Protection System
 * Protected Content Script with CIH99 Protection System
 */

// متغيرات النظام
let isExtensionActive = false;
let protectionCheckInterval = null;

/**
 * تهيئة Content Script المحمي
 */
async function initializeProtectedContent() {
    try {
        console.log('🔐 تهيئة Content Script المحمي...');
        
        // التحقق من حالة الحماية
        await checkProtectionStatus();
        
        // إعداد مستمعي الأحداث
        setupProtectedListeners();
        
        // بدء المراقبة الدورية
        startProtectionMonitoring();
        
        console.log('✅ تم تهيئة Content Script المحمي بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة Content Script المحمي:', error);
    }
}

/**
 * التحقق من حالة الحماية
 */
async function checkProtectionStatus() {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'getStatus'
        });
        
        if (response && response.enabled && response.passwordVerified) {
            isExtensionActive = true;
            console.log('✅ الإضافة نشطة ومحمية');
            enableExtensionFeatures();
        } else {
            isExtensionActive = false;
            console.log('🚫 الإضافة غير نشطة أو غير محمية');
            disableExtensionFeatures();
        }
        
    } catch (error) {
        console.error('❌ خطأ في التحقق من حالة الحماية:', error);
        isExtensionActive = false;
        disableExtensionFeatures();
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupProtectedListeners() {
    // مستمع رسائل من background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('📨 رسالة واردة في Content Script:', message.action);
        
        switch (message.action) {
            case 'protectionStatusChanged':
                handleProtectionStatusChange(message.enabled);
                sendResponse({ success: true });
                break;
                
            case 'updateStatus':
                isExtensionActive = message.enabled;
                if (message.enabled) {
                    enableExtensionFeatures();
                } else {
                    disableExtensionFeatures();
                }
                sendResponse({ success: true });
                break;
                
            default:
                // رسائل أخرى
                break;
        }
        
        return true;
    });
    
    // مستمع أحداث الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        if (isExtensionActive) {
            initializePageFeatures();
        }
    });
    
    // مستمع تغيير URL
    let currentUrl = window.location.href;
    const urlObserver = new MutationObserver(() => {
        if (window.location.href !== currentUrl) {
            currentUrl = window.location.href;
            console.log('🔄 تغيير URL:', currentUrl);
            
            if (isExtensionActive) {
                handleUrlChange();
            }
        }
    });
    
    urlObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * معالج تغيير حالة الحماية
 */
function handleProtectionStatusChange(enabled) {
    console.log('🔄 تغيير حالة الحماية:', enabled);
    
    isExtensionActive = enabled;
    
    if (enabled) {
        enableExtensionFeatures();
        showProtectionNotification('تم تفعيل الإضافة بنجاح', 'success');
    } else {
        disableExtensionFeatures();
        showProtectionNotification('تم تعطيل الإضافة', 'warning');
    }
}

/**
 * تفعيل ميزات الإضافة
 */
function enableExtensionFeatures() {
    console.log('✅ تفعيل ميزات الإضافة');
    
    // إضافة أزرار التحميل
    addDownloadButtons();
    
    // تفعيل اكتشاف الفيديوهات
    enableVideoDetection();
    
    // إضافة واجهة المستخدم
    addExtensionUI();
    
    // إضافة الأنماط
    addExtensionStyles();
}

/**
 * تعطيل ميزات الإضافة
 */
function disableExtensionFeatures() {
    console.log('🚫 تعطيل ميزات الإضافة');
    
    // إزالة أزرار التحميل
    removeDownloadButtons();
    
    // تعطيل اكتشاف الفيديوهات
    disableVideoDetection();
    
    // إزالة واجهة المستخدم
    removeExtensionUI();
}

/**
 * إضافة أزرار التحميل
 */
function addDownloadButtons() {
    // البحث عن عناصر الفيديو
    const videoElements = document.querySelectorAll('video, [data-purpose="video-viewer"]');
    
    videoElements.forEach((element, index) => {
        // التحقق من وجود زر التحميل مسبقاً
        if (element.parentElement.querySelector('.cih99-download-btn')) {
            return;
        }
        
        // إنشاء زر التحميل
        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'cih99-download-btn';
        downloadBtn.innerHTML = '⬇️ تحميل';
        downloadBtn.title = 'تحميل الفيديو - CIH99 Downloader';
        
        // إضافة مستمع النقر
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleDownloadClick(element);
        });
        
        // إضافة الزر للصفحة
        element.parentElement.style.position = 'relative';
        element.parentElement.appendChild(downloadBtn);
    });
}

/**
 * إزالة أزرار التحميل
 */
function removeDownloadButtons() {
    const downloadBtns = document.querySelectorAll('.cih99-download-btn');
    downloadBtns.forEach(btn => btn.remove());
}

/**
 * معالج النقر على زر التحميل
 */
async function handleDownloadClick(videoElement) {
    if (!isExtensionActive) {
        showProtectionNotification('الإضافة غير مفعلة. يرجى إدخال كلمة المرور.', 'error');
        return;
    }
    
    try {
        console.log('⬇️ بدء عملية التحميل...');
        
        // الحصول على معلومات الفيديو
        const videoInfo = extractVideoInfo(videoElement);
        
        if (!videoInfo.url) {
            showProtectionNotification('لم يتم العثور على رابط الفيديو', 'error');
            return;
        }
        
        // إرسال طلب التحميل للخلفية
        const response = await chrome.runtime.sendMessage({
            action: 'download',
            url: videoInfo.url,
            title: videoInfo.title,
            quality: videoInfo.quality
        });
        
        if (response.success) {
            showProtectionNotification('تم بدء التحميل بنجاح', 'success');
        } else {
            showProtectionNotification(response.error || 'فشل في بدء التحميل', 'error');
        }
        
    } catch (error) {
        console.error('❌ خطأ في التحميل:', error);
        showProtectionNotification('حدث خطأ أثناء التحميل', 'error');
    }
}

/**
 * استخراج معلومات الفيديو
 */
function extractVideoInfo(videoElement) {
    const info = {
        url: null,
        title: 'Unknown Video',
        quality: 'auto'
    };
    
    // محاولة الحصول على رابط الفيديو
    if (videoElement.src) {
        info.url = videoElement.src;
    } else if (videoElement.currentSrc) {
        info.url = videoElement.currentSrc;
    }
    
    // محاولة الحصول على العنوان
    const titleElement = document.querySelector('h1, .video-title, [data-purpose="video-title"]');
    if (titleElement) {
        info.title = titleElement.textContent.trim();
    }
    
    return info;
}

/**
 * تفعيل اكتشاف الفيديوهات
 */
function enableVideoDetection() {
    // مراقبة إضافة عناصر جديدة للصفحة
    const observer = new MutationObserver((mutations) => {
        if (!isExtensionActive) return;
        
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const videos = node.querySelectorAll ? node.querySelectorAll('video') : [];
                    if (videos.length > 0) {
                        setTimeout(() => addDownloadButtons(), 1000);
                    }
                }
            });
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * تعطيل اكتشاف الفيديوهات
 */
function disableVideoDetection() {
    // سيتم تنفيذها حسب الحاجة
}

/**
 * إضافة واجهة المستخدم
 */
function addExtensionUI() {
    // إضافة شريط الحالة
    if (!document.querySelector('.cih99-status-bar')) {
        const statusBar = document.createElement('div');
        statusBar.className = 'cih99-status-bar';
        statusBar.innerHTML = '🟢 CIH99 Downloader Active';
        document.body.appendChild(statusBar);
    }
}

/**
 * إزالة واجهة المستخدم
 */
function removeExtensionUI() {
    const statusBar = document.querySelector('.cih99-status-bar');
    if (statusBar) {
        statusBar.remove();
    }
}

/**
 * إضافة الأنماط
 */
function addExtensionStyles() {
    if (document.querySelector('#cih99-styles')) return;
    
    const styles = document.createElement('style');
    styles.id = 'cih99-styles';
    styles.textContent = `
        .cih99-download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            z-index: 9999;
            transition: all 0.3s ease;
        }
        
        .cih99-download-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
        }
        
        .cih99-status-bar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }
        
        .cih99-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 10001;
            animation: slideIn 0.3s ease-out;
        }
        
        .cih99-notification.success {
            background: #4CAF50;
            color: white;
        }
        
        .cih99-notification.error {
            background: #f44336;
            color: white;
        }
        
        .cih99-notification.warning {
            background: #ff9800;
            color: white;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    
    document.head.appendChild(styles);
}

/**
 * عرض إشعار الحماية
 */
function showProtectionNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.cih99-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // إنشاء إشعار جديد
    const notification = document.createElement('div');
    notification.className = `cih99-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

/**
 * بدء مراقبة الحماية
 */
function startProtectionMonitoring() {
    // فحص دوري كل 10 ثوانٍ
    protectionCheckInterval = setInterval(checkProtectionStatus, 10000);
}

/**
 * معالج تغيير URL
 */
function handleUrlChange() {
    console.log('🔄 معالجة تغيير URL');
    
    // إعادة إضافة أزرار التحميل بعد تأخير قصير
    setTimeout(() => {
        if (isExtensionActive) {
            addDownloadButtons();
        }
    }, 2000);
}

/**
 * تهيئة ميزات الصفحة
 */
function initializePageFeatures() {
    console.log('🚀 تهيئة ميزات الصفحة');
    
    // إضافة أزرار التحميل
    addDownloadButtons();
    
    // إضافة واجهة المستخدم
    addExtensionUI();
}

// تهيئة النظام عند تحميل الملف
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProtectedContent);
} else {
    initializeProtectedContent();
}

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (protectionCheckInterval) {
        clearInterval(protectionCheckInterval);
    }
});

console.log('🔐 تم تحميل Content Script المحمي - CIH99 Protection System');
