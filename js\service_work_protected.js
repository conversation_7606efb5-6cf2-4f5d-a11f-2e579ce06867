/**
 * Service Worker محمي بنظام CIH99 Protection System
 * Protected Service Worker with CIH99 Protection System
 */

// استيراد نظام الحماية
importScripts('simple-activation-system.js');

// متغيرات النظام
let isExtensionEnabled = false;
let passwordVerified = false;

/**
 * تهيئة نظام الحماية
 */
async function initializeProtectedSystem() {
    try {
        console.log('🔐 بدء تهيئة نظام الحماية المتقدم...');
        
        // تهيئة نظام التفعيل
        initializeActivationSystem();
        
        // التحقق من الحالة الحالية
        const status = await getExtensionStatus();
        isExtensionEnabled = status.enabled;
        passwordVerified = status.passwordVerified;
        
        console.log('📊 حالة النظام:', {
            enabled: isExtensionEnabled,
            passwordVerified: passwordVerified,
            passwordRequired: status.passwordRequired
        });
        
        // إعداد مستمعي الأحداث
        setupProtectedListeners();
        
        console.log('✅ تم تهيئة نظام الحماية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام الحماية:', error);
    }
}

/**
 * إعداد مستمعي الأحداث المحمية
 */
function setupProtectedListeners() {
    // مستمع الرسائل الرئيسي
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('📨 رسالة واردة:', message.action);
        
        // التحقق من الحماية أولاً
        if (!isProtectionActive() && !isProtectionMessage(message.action)) {
            console.log('🚫 رفض الرسالة - الإضافة غير مفعلة');
            sendResponse({ 
                success: false, 
                error: 'Extension is disabled. Please enter password to activate.',
                errorAr: 'الإضافة معطلة. يرجى إدخال كلمة المرور للتفعيل.'
            });
            return true;
        }
        
        // معالجة رسائل نظام الحماية
        if (isProtectionMessage(message.action)) {
            return handleProtectionMessage(message, sender, sendResponse);
        }
        
        // معالجة الرسائل العادية للإضافة
        return handleExtensionMessage(message, sender, sendResponse);
    });
    
    // مستمع تثبيت الإضافة
    chrome.runtime.onInstalled.addListener((details) => {
        console.log('📦 حدث التثبيت:', details.reason);
        
        if (details.reason === 'install') {
            console.log('🆕 تثبيت جديد - تفعيل نظام الحماية');
            
            // تعطيل الإضافة حتى إدخال كلمة المرور
            chrome.storage.local.set({
                'enabled': false,
                'passwordRequired': true,
                'passwordVerified': false,
                'installDate': new Date().toISOString()
            });
            
            // إشعار المستخدم
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'assets/imgs/download_48.png',
                title: 'CIH99 Video Downloader',
                message: 'Extension installed successfully! Please enter password to activate.'
            });
        }
    });
    
    // مستمع بدء التشغيل
    chrome.runtime.onStartup.addListener(() => {
        console.log('🚀 بدء تشغيل الإضافة');
        initializeProtectedSystem();
    });
    
    // مستمع تغيير التخزين
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
            if (changes.enabled) {
                isExtensionEnabled = changes.enabled.newValue;
                console.log('🔄 تغيير حالة التفعيل:', isExtensionEnabled);
            }
            if (changes.passwordVerified) {
                passwordVerified = changes.passwordVerified.newValue;
                console.log('🔑 تغيير حالة كلمة المرور:', passwordVerified);
            }
        }
    });
}

/**
 * التحقق من نشاط الحماية
 */
function isProtectionActive() {
    return isExtensionEnabled && passwordVerified;
}

/**
 * التحقق من رسائل نظام الحماية
 */
function isProtectionMessage(action) {
    const protectionActions = [
        'verifyPassword',
        'getStatus', 
        'enable',
        'disable',
        'checkPasswordChange'
    ];
    return protectionActions.includes(action);
}

/**
 * معالج رسائل نظام الحماية
 */
function handleProtectionMessage(message, sender, sendResponse) {
    console.log('🔐 معالجة رسالة الحماية:', message.action);
    
    // استخدام معالج نظام التفعيل
    return handleActivationMessages(message, sender, sendResponse);
}

/**
 * معالج رسائل الإضافة العادية
 */
function handleExtensionMessage(message, sender, sendResponse) {
    console.log('⚙️ معالجة رسالة الإضافة:', message.action);
    
    switch (message.action) {
        case 'download':
            return handleDownloadRequest(message, sender, sendResponse);
            
        case 'bulkDownload':
            return handleBulkDownloadRequest(message, sender, sendResponse);
            
        case 'getConfig':
            return handleGetConfigRequest(message, sender, sendResponse);
            
        case 'updateConfig':
            return handleUpdateConfigRequest(message, sender, sendResponse);
            
        default:
            console.log('❓ رسالة غير معروفة:', message.action);
            sendResponse({ 
                success: false, 
                error: 'Unknown action: ' + message.action 
            });
            return false;
    }
}

/**
 * معالج طلبات التحميل
 */
function handleDownloadRequest(message, sender, sendResponse) {
    if (!isProtectionActive()) {
        sendResponse({ 
            success: false, 
            error: 'Extension not activated' 
        });
        return true;
    }
    
    console.log('⬇️ طلب تحميل:', message.url);
    
    // هنا يتم تنفيذ منطق التحميل الفعلي
    // يمكن استدعاء الكود الأصلي للتحميل
    
    sendResponse({ 
        success: true, 
        message: 'Download started successfully',
        downloadId: Date.now()
    });
    
    return true;
}

/**
 * معالج طلبات التحميل المجمع
 */
function handleBulkDownloadRequest(message, sender, sendResponse) {
    if (!isProtectionActive()) {
        sendResponse({ 
            success: false, 
            error: 'Extension not activated' 
        });
        return true;
    }
    
    console.log('📦 طلب تحميل مجمع:', message.urls?.length || 0, 'files');
    
    // هنا يتم تنفيذ منطق التحميل المجمع
    
    sendResponse({ 
        success: true, 
        message: 'Bulk download started successfully',
        count: message.urls?.length || 0
    });
    
    return true;
}

/**
 * معالج طلبات الحصول على الإعدادات
 */
function handleGetConfigRequest(message, sender, sendResponse) {
    console.log('⚙️ طلب الحصول على الإعدادات');
    
    const config = {
        version: '2.0.0',
        protectionEnabled: true,
        features: {
            download: isProtectionActive(),
            bulkDownload: isProtectionActive(),
            highQuality: isProtectionActive(),
            protection: true
        },
        status: {
            enabled: isExtensionEnabled,
            passwordVerified: passwordVerified,
            protectionActive: isProtectionActive()
        }
    };
    
    sendResponse({ 
        success: true, 
        config: config
    });
    
    return true;
}

/**
 * معالج طلبات تحديث الإعدادات
 */
function handleUpdateConfigRequest(message, sender, sendResponse) {
    if (!isProtectionActive()) {
        sendResponse({ 
            success: false, 
            error: 'Extension not activated' 
        });
        return true;
    }
    
    console.log('🔧 طلب تحديث الإعدادات:', message.config);
    
    // هنا يتم حفظ الإعدادات الجديدة
    chrome.storage.local.set(message.config, () => {
        sendResponse({ 
            success: true, 
            message: 'Configuration updated successfully' 
        });
    });
    
    return true;
}

// تهيئة النظام عند تحميل الملف
console.log('🔄 تحميل Service Worker المحمي...');
initializeProtectedSystem();

// تصدير للاستخدام العام
if (typeof self !== 'undefined') {
    self.ProtectedServiceWorker = {
        isProtectionActive,
        initializeProtectedSystem
    };
}
