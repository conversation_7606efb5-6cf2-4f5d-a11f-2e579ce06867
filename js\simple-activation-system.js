/**
 * نظام التفعيل البسيط - CIH99 Simple Activation System
 * ملف واحد يحتوي على كل ما تحتاجه لنظام التفعيل
 * 
 * كيفية الاستخدام:
 * 1. ضع هذا الملف في مشروعك
 * 2. غير رابط كلمات المرور في المتغير PASSWORD_URL
 * 3. استخدم الدوال المتاحة في مشروعك
 */

// ===== الإعدادات الأساسية =====
const PASSWORD_URL = 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json';
const CHECK_INTERVAL = 30000; // 30 ثانية

// ===== متغيرات النظام =====
let extensionEnabled = true;
let checkTimer = null;

// ===== دوال التحقق من كلمة المرور =====

/**
 * التحقق من صحة كلمة المرور
 */
async function verifyPassword(password) {
    try {
        const randomParam = Math.random();
        const url = `${PASSWORD_URL}?nocache=${randomParam}`;
        
        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`فشل الطلب: ${response.status}`);
        }

        const data = await response.json();
        
        // استخراج جميع كلمات المرور
        let allPasswords = [];
        if (data.passwords) allPasswords = [...allPasswords, ...data.passwords];
        if (data.additionalPasswords) allPasswords = [...allPasswords, ...data.additionalPasswords];
        if (data.specialPasswords) allPasswords = [...allPasswords, ...data.specialPasswords];

        // التحقق من كلمة المرور
        if (allPasswords.includes(password)) {
            // كلمة المرور صحيحة
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.set({
                    passwordVerified: true,
                    passwordRequired: false,
                    enabled: true
                });
            } else {
                // للاختبار المحلي
                localStorage.setItem('passwordVerified', 'true');
                localStorage.setItem('passwordRequired', 'false');
                localStorage.setItem('enabled', 'true');
            }

            enableExtension();
            return { success: true, message: 'تم تفعيل الإضافة بنجاح!' };
        } else {
            return { success: false, message: 'كلمة المرور غير صحيحة' };
        }

    } catch (error) {
        console.error('خطأ في التحقق من كلمة المرور:', error);
        return { success: false, message: 'حدث خطأ في التحقق من كلمة المرور' };
    }
}

/**
 * التحقق من تغيير كلمة المرور
 */
async function checkPasswordChange() {
    try {
        const randomParam = Math.random();
        const url = `${PASSWORD_URL}?nocache=${randomParam}`;
        
        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) return;

        const data = await response.json();
        const currentPasswordsJSON = JSON.stringify(data);

        // الحصول على البيانات المحفوظة
        chrome.storage.local.get(['lastPasswordsJSON'], (result) => {
            const lastPasswordsJSON = result.lastPasswordsJSON;

            if (lastPasswordsJSON && lastPasswordsJSON !== currentPasswordsJSON) {
                // تم تغيير كلمات المرور
                console.log('تم اكتشاف تغيير في كلمات المرور');
                
                // تحديث البيانات وتعطيل الإضافة
                chrome.storage.local.set({
                    'lastPasswordsJSON': currentPasswordsJSON,
                    'passwordRequired': true,
                    'passwordVerified': false,
                    'enabled': false
                });

                disableExtension();
                
                // إشعار المستخدم
                if (chrome.notifications) {
                    chrome.notifications.create({
                        type: 'basic',
                        iconUrl: 'icons/icon48.png',
                        title: 'تم تغيير كلمة المرور',
                        message: 'تم تعطيل الإضافة. يرجى إدخال كلمة المرور الجديدة.'
                    });
                }
            } else {
                // لم يتم تغيير كلمات المرور
                if (currentPasswordsJSON !== lastPasswordsJSON) {
                    chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
                }
            }
        });

    } catch (error) {
        console.error('خطأ في التحقق من تغيير كلمة المرور:', error);
    }
}

// ===== دوال إدارة التفعيل =====

/**
 * تفعيل الإضافة
 */
function enableExtension() {
    extensionEnabled = true;
    chrome.storage.local.set({ 'enabled': true });
    updateAllTabs(true);
    console.log('تم تفعيل الإضافة');
}

/**
 * تعطيل الإضافة
 */
function disableExtension() {
    extensionEnabled = false;
    chrome.storage.local.set({ 'enabled': false });
    updateAllTabs(false);
    console.log('تم تعطيل الإضافة');
}

/**
 * تحديث جميع التابات
 */
function updateAllTabs(enabled) {
    chrome.tabs.query({}, (tabs) => {
        tabs.forEach((tab) => {
            if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateStatus',
                    enabled: enabled
                }).catch(() => {
                    // تجاهل الأخطاء
                });
            }
        });
    });
}

/**
 * الحصول على حالة الإضافة
 */
function getExtensionStatus() {
    return new Promise((resolve) => {
        chrome.storage.local.get(['enabled', 'passwordRequired', 'passwordVerified'], (data) => {
            resolve({
                enabled: data.enabled !== false,
                passwordRequired: data.passwordRequired === true,
                passwordVerified: data.passwordVerified === true
            });
        });
    });
}

// ===== دوال المراقبة =====

/**
 * بدء مراقبة تغيير كلمات المرور
 */
function startPasswordMonitoring() {
    // التحقق الفوري
    checkPasswordChange();
    
    // التحقق الدوري
    if (checkTimer) clearInterval(checkTimer);
    checkTimer = setInterval(checkPasswordChange, CHECK_INTERVAL);
    
    console.log('تم بدء مراقبة كلمات المرور');
}

/**
 * إيقاف مراقبة كلمات المرور
 */
function stopPasswordMonitoring() {
    if (checkTimer) {
        clearInterval(checkTimer);
        checkTimer = null;
    }
    console.log('تم إيقاف مراقبة كلمات المرور');
}

// ===== دوال واجهة المستخدم =====

/**
 * إعداد واجهة المستخدم (للاستخدام في popup.js)
 */
function setupActivationUI() {
    const toggleSwitch = document.getElementById('toggleSwitch');
    const statusText = document.getElementById('statusText');
    const passwordSection = document.getElementById('passwordSection');
    const passwordInput = document.getElementById('passwordInput');
    const submitPassword = document.getElementById('submitPassword');
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');

    // تحديث الواجهة حسب الحالة الحالية
    updateUI();

    // مفتاح التفعيل/التعطيل
    if (toggleSwitch) {
        toggleSwitch.addEventListener('change', async function() {
            if (this.checked) {
                const status = await getExtensionStatus();
                if (status.passwordRequired && !status.passwordVerified) {
                    this.checked = false;
                    statusText.textContent = 'معطل';
                    showPasswordSection();
                    showMessage('يرجى إدخال كلمة المرور لتفعيل الإضافة');
                } else {
                    enableExtension();
                    statusText.textContent = 'نشط';
                }
            } else {
                disableExtension();
                statusText.textContent = 'معطل';
            }
        });
    }

    // زر إرسال كلمة المرور
    if (submitPassword) {
        submitPassword.addEventListener('click', handlePasswordSubmit);
    }

    // إدخال كلمة المرور بـ Enter
    if (passwordInput) {
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handlePasswordSubmit();
            }
        });
    }

    async function handlePasswordSubmit() {
        const password = passwordInput.value.trim();
        if (!password) {
            showMessage('يرجى إدخال كلمة المرور');
            return;
        }

        showMessage('جاري التحقق من كلمة المرور...');
        
        const result = await verifyPassword(password);
        
        if (result.success) {
            toggleSwitch.checked = true;
            statusText.textContent = 'نشط';
            showMessage(result.message, true);
            setTimeout(() => {
                hidePasswordSection();
            }, 1000);
        } else {
            showMessage(result.message);
            passwordInput.value = '';
        }
    }

    async function updateUI() {
        const status = await getExtensionStatus();
        
        if (status.enabled && (!status.passwordRequired || status.passwordVerified)) {
            toggleSwitch.checked = true;
            statusText.textContent = 'نشط';
            hidePasswordSection();
        } else {
            toggleSwitch.checked = false;
            statusText.textContent = 'معطل';
            if (status.passwordRequired && !status.passwordVerified) {
                showPasswordSection();
            }
        }
    }

    function showPasswordSection() {
        if (passwordSection) {
            passwordSection.style.display = 'block';
            if (passwordInput) passwordInput.focus();
        }
    }

    function hidePasswordSection() {
        if (passwordSection) {
            passwordSection.style.display = 'none';
        }
        if (passwordInput) {
            passwordInput.value = '';
        }
    }

    function showMessage(message, isSuccess = false) {
        if (messageBox && messageText) {
            messageText.textContent = message;
            messageBox.className = isSuccess ? 'message-box success' : 'message-box';
            messageBox.style.display = 'block';
            
            setTimeout(() => {
                messageBox.style.display = 'none';
            }, 5000);
        }
    }
}

// ===== التهيئة الأولية =====

/**
 * تهيئة النظام (للاستخدام في background.js)
 */
function initializeActivationSystem() {
    // تعيين الحالة الأولية
    chrome.storage.local.get('enabled', (data) => {
        if (data.enabled === undefined) {
            chrome.storage.local.set({ 'enabled': true });
        } else {
            extensionEnabled = data.enabled;
        }
    });

    // تعيين حالة كلمة المرور الأولية
    chrome.storage.local.set({
        'passwordRequired': true,
        'passwordVerified': false
    });

    // بدء المراقبة
    startPasswordMonitoring();

    console.log('تم تهيئة نظام التفعيل');
}

/**
 * تهيئة واجهة المستخدم عند تحميل الصفحة
 */
function initializePopupUI() {
    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupActivationUI);
    } else {
        setupActivationUI();
    }
}

// ===== معالج الرسائل (للاستخدام في background.js) =====

/**
 * معالج الرسائل الرئيسي
 */
function handleActivationMessages(message, sender, sendResponse) {
    switch (message.action) {
        case 'enable':
            enableExtension();
            sendResponse({ success: true });
            break;
            
        case 'disable':
            disableExtension();
            sendResponse({ success: true });
            break;
            
        case 'getStatus':
            getExtensionStatus().then(status => {
                sendResponse(status);
            });
            return true;
            
        case 'checkPasswordChange':
            checkPasswordChange();
            sendResponse({ success: true });
            break;
            
        case 'verifyPassword':
            verifyPassword(message.password).then(result => {
                sendResponse(result);
            });
            return true;
            
        default:
            return false;
    }
    return false;
}

// تصدير الدوال للاستخدام
if (typeof window !== 'undefined') {
    // في بيئة المتصفح
    window.ActivationSystem = {
        initializeActivationSystem,
        handleActivationMessages,
        setupActivationUI,
        initializePopupUI,
        verifyPassword,
        enableExtension,
        disableExtension,
        getExtensionStatus,
        startPasswordMonitoring,
        stopPasswordMonitoring
    };

    // تهيئة واجهة المستخدم تلقائياً
    initializePopupUI();
}
