/**
 * ملف دمج نظام الحماية مع service worker الحالي
 * CIH99 Protection System Integration
 */

// استيراد نظام التفعيل
importScripts('simple-activation-system.js');

// متغيرات النظام
let isProtectionActive = false;
let originalServiceWorker = null;

/**
 * تهيئة نظام الحماية
 */
async function initializeProtection() {
    try {
        // تهيئة نظام التفعيل
        initializeActivationSystem();
        
        // التحقق من حالة التفعيل
        const status = await getExtensionStatus();
        isProtectionActive = status.enabled && (!status.passwordRequired || status.passwordVerified);
        
        console.log('تم تهيئة نظام الحماية:', isProtectionActive);
        
        // إعداد مستمعي الأحداث
        setupProtectionListeners();
        
    } catch (error) {
        console.error('خطأ في تهيئة نظام الحماية:', error);
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupProtectionListeners() {
    // مستمع الرسائل
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        // التحقق من الحماية أولاً
        if (!isProtectionActive && message.action !== 'verifyPassword' && message.action !== 'getStatus') {
            sendResponse({ 
                success: false, 
                error: 'الإضافة معطلة. يرجى إدخال كلمة المرور للتفعيل.' 
            });
            return true;
        }
        
        // معالجة رسائل نظام الحماية
        if (handleActivationMessages(message, sender, sendResponse)) {
            return true;
        }
        
        // تمرير الرسائل الأخرى للمعالج الأصلي
        return handleOriginalMessages(message, sender, sendResponse);
    });
    
    // مستمع تثبيت الإضافة
    chrome.runtime.onInstalled.addListener((details) => {
        if (details.reason === 'install') {
            console.log('تم تثبيت الإضافة - تفعيل نظام الحماية');
            // تعطيل الإضافة حتى إدخال كلمة المرور
            chrome.storage.local.set({
                'enabled': false,
                'passwordRequired': true,
                'passwordVerified': false
            });
        }
    });
    
    // مستمع بدء تشغيل الإضافة
    chrome.runtime.onStartup.addListener(() => {
        console.log('بدء تشغيل الإضافة - فحص نظام الحماية');
        initializeProtection();
    });
}

/**
 * معالج الرسائل الأصلي (placeholder)
 */
function handleOriginalMessages(message, sender, sendResponse) {
    // هنا يمكن إضافة معالجة الرسائل الأصلية للإضافة
    // أو تمريرها للمعالج الأصلي إذا كان موجوداً
    
    switch (message.action) {
        case 'download':
            if (isProtectionActive) {
                // السماح بالتحميل فقط إذا كانت الحماية نشطة
                return handleDownload(message, sender, sendResponse);
            } else {
                sendResponse({ 
                    success: false, 
                    error: 'يجب تفعيل الإضافة أولاً' 
                });
                return true;
            }
            
        case 'getConfig':
            if (isProtectionActive) {
                return handleGetConfig(message, sender, sendResponse);
            } else {
                sendResponse({ 
                    success: false, 
                    error: 'الإضافة غير مفعلة' 
                });
                return true;
            }
            
        default:
            // رسائل غير معروفة
            sendResponse({ success: false, error: 'رسالة غير معروفة' });
            return false;
    }
}

/**
 * معالج التحميل (مثال)
 */
function handleDownload(message, sender, sendResponse) {
    // هنا يتم معالجة طلبات التحميل
    console.log('معالجة طلب التحميل:', message);
    
    // مثال على المعالجة
    sendResponse({ 
        success: true, 
        message: 'تم بدء التحميل بنجاح' 
    });
    
    return true;
}

/**
 * معالج الحصول على الإعدادات (مثال)
 */
function handleGetConfig(message, sender, sendResponse) {
    // هنا يتم إرجاع إعدادات الإضافة
    console.log('طلب الحصول على الإعدادات');
    
    sendResponse({ 
        success: true, 
        config: {
            version: '2.0.0',
            protectionEnabled: true,
            features: ['download', 'bulk_download', 'high_quality']
        }
    });
    
    return true;
}

/**
 * تحديث حالة الحماية
 */
async function updateProtectionStatus() {
    try {
        const status = await getExtensionStatus();
        const newStatus = status.enabled && (!status.passwordRequired || status.passwordVerified);
        
        if (newStatus !== isProtectionActive) {
            isProtectionActive = newStatus;
            console.log('تم تحديث حالة الحماية:', isProtectionActive);
            
            // إشعار جميع التابات بالتغيير
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach((tab) => {
                    if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'protectionStatusChanged',
                            enabled: isProtectionActive
                        }).catch(() => {
                            // تجاهل الأخطاء
                        });
                    }
                });
            });
        }
    } catch (error) {
        console.error('خطأ في تحديث حالة الحماية:', error);
    }
}

/**
 * مراقبة دورية لحالة الحماية
 */
setInterval(updateProtectionStatus, 5000); // كل 5 ثوانٍ

// تهيئة النظام عند تحميل الملف
initializeProtection();

// تصدير الدوال للاستخدام
if (typeof self !== 'undefined') {
    self.ProtectionIntegration = {
        initializeProtection,
        updateProtectionStatus,
        isProtectionActive: () => isProtectionActive
    };
}
